# Model Converter

This utility converts PyTorch model files from `.pth` format to `.pt` (TorchScript) format.

## Usage

### Simple Conversion

To convert the default model file (`model_distill_bert.pth`):

```bash
python convert_model.py
```

### Advanced Usage

To specify a custom model path:

```bash
python convert_model.py --model-path /path/to/your/model.pth
```

To specify the number of labels (if auto-detection fails):

```bash
python convert_model.py --num-labels 20
```

### Batch Scripts

For Windows users:
```cmd
convert_model.bat
```

For Linux/Mac users:
```bash
chmod +x convert_model.sh
./convert_model.sh
```

## What it does

1. **Loads the original model**: Reads the `.pth` file containing the trained DistilBERT model
2. **Auto-detects parameters**: Automatically determines the number of labels from the model
3. **Converts to TorchScript**: Creates a `.pt` file that can be loaded faster and is more portable
4. **Verifies the conversion**: Ensures the converted model can be loaded successfully

## Requirements

- Python 3.7+
- PyTorch
- Transformers library
- The original `.pth` model file

## Output

The converter creates a new file with the same name but `.pt` extension in the same directory as the original model.

For example:
- Input: `model_distill_bert.pth`
- Output: `model_distill_bert.pt`

## Troubleshooting

### Model not found
Make sure the model file exists in the expected location:
- Default: `./app/data4c/results/model_distill_bert.pth`
- Or specify the path with `--model-path`

### Number of labels error
If auto-detection fails, manually specify the number of labels:
```bash
python convert_model.py --num-labels 20
```

### Memory issues
The conversion loads the entire model into memory. If you encounter memory issues:
1. Close other applications
2. Use a machine with more RAM
3. Consider using a smaller model

## File Formats

- **`.pth`**: PyTorch state dictionary format (original)
- **`.pt`**: TorchScript format (converted)

The TorchScript format offers several advantages:
- Faster loading times
- Better portability across different environments
- Optimized for inference
- Can be used without Python in some cases
