@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Model Converter: .pth to .pt format
echo ========================================
echo.

REM Change to the directory where this batch file is located
cd /d "%~dp0"
echo Current directory: %CD%
echo.

REM Try different Python commands
set PYTHON_CMD=
for %%p in (python python3 py) do (
    %%p --version >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        set PYTHON_CMD=%%p
        goto :found_python
    )
)

echo Error: Python is not installed or not in PATH
echo.
echo Please install Python from https://python.org
echo Make sure to check "Add Python to PATH" during installation
echo.
goto :error_exit

:found_python
echo Found Python: !PYTHON_CMD!
!PYTHON_CMD! --version
echo.

REM Check if the convert_model.py file exists
if not exist "convert_model.py" (
    echo Error: convert_model.py not found in current directory
    echo.
    echo Expected location: %CD%\convert_model.py
    echo.
    echo Please make sure you're running this from the backend4c folder
    goto :error_exit
)

REM Check if required directories exist
if not exist "app" (
    echo Warning: 'app' directory not found
    echo This might cause import errors
    echo.
)

if not exist "app\data4c\results" (
    echo Warning: 'app\data4c\results' directory not found
    echo Creating directory...
    mkdir "app\data4c\results" 2>nul
)

REM Check if model file exists
if exist "app\data4c\results\model_distill_bert.pth" (
    echo Found model file: app\data4c\results\model_distill_bert.pth
    for %%A in ("app\data4c\results\model_distill_bert.pth") do (
        set /a size=%%~zA/1024/1024
        echo Model size: !size! MB
    )
    echo.
) else (
    echo Warning: Default model file not found at app\data4c\results\model_distill_bert.pth
    echo You can still run the converter and specify a custom path
    echo.
)

REM Try to run the conversion
echo Running conversion...
echo Command: !PYTHON_CMD! convert_model.py
echo.

!PYTHON_CMD! convert_model.py

set RESULT=%ERRORLEVEL%

echo.
if %RESULT% EQU 0 (
    echo ========================================
    echo ✅ Conversion completed successfully!
    echo ========================================
    
    REM Show the converted file info
    if exist "app\data4c\results\model_distill_bert.pt" (
        echo.
        echo Converted file created:
        for %%A in ("app\data4c\results\model_distill_bert.pt") do (
            set /a size=%%~zA/1024/1024
            echo   File: app\data4c\results\model_distill_bert.pt
            echo   Size: !size! MB
            echo   Date: %%~tA
        )
    )
) else (
    echo ========================================
    echo ❌ Conversion failed!
    echo ========================================
    echo.
    echo Common solutions:
    echo 1. Make sure the model file exists
    echo 2. Install required packages: pip install torch transformers
    echo 3. Check that you have enough memory
    echo 4. Run from the correct directory (backend4c)
)

echo.
goto :normal_exit

:error_exit
echo.
echo ========================================
echo Setup Error - Cannot Continue
echo ========================================
echo.

:normal_exit
echo Press any key to exit...
pause >nul
