#!/usr/bin/env python3
"""
Migration script to convert from DistilBERT to MiniMind system
"""

import os
import sys
import shutil
from pathlib import Path
from app.utils.converter import DataConverter, ModelConverter
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

def main():
    """Main conversion function"""
    logger.info("Starting conversion from DistilBERT to MiniMind")
    
    # Paths
    current_dir = Path(__file__).parent
    data_dir = current_dir / "app" / "data4c"
    old_model_path = data_dir / "results" / "model_distill_bert.pth"
    new_model_path = data_dir / "results" / "minimind"
    training_data_path = data_dir / "customer_queries.csv"
    
    # Create converter instances
    data_converter = DataConverter()
    model_converter = ModelConverter()
    
    # Step 1: Validate existing training data
    logger.info("Step 1: Validating training data")
    if training_data_path.exists():
        validation_results = data_converter.validate_training_data(str(training_data_path))
        if validation_results["valid"]:
            logger.info(f"Training data is valid: {validation_results['total_records']} records")
        else:
            logger.warning(f"Training data has issues: {validation_results.get('issues', [])}")
    else:
        logger.info("No existing training data found. Creating sample data...")
        data_converter.create_sample_data(str(training_data_path), num_samples=100)
    
    # Step 2: Convert data format if needed
    logger.info("Step 2: Converting data format")
    backup_data_path = data_dir / "customer_queries_backup.csv"
    if training_data_path.exists() and not backup_data_path.exists():
        # Create backup
        shutil.copy2(training_data_path, backup_data_path)
        logger.info(f"Created backup at {backup_data_path}")
        
        # Convert data format
        success = data_converter.convert_distilbert_to_minimind_data(
            str(training_data_path), 
            str(training_data_path)
        )
        if success:
            logger.info("Data format conversion successful")
        else:
            logger.error("Data format conversion failed")
            return False
    
    # Step 3: Migrate model
    logger.info("Step 3: Migrating model")
    success = data_converter.migrate_distilbert_to_minimind(
        str(old_model_path),
        str(new_model_path),
        str(training_data_path)
    )
    
    if success:
        logger.info("Model migration successful")
    else:
        logger.error("Model migration failed")
        return False
    
    # Step 4: Create additional data formats
    logger.info("Step 4: Creating additional data formats")
    
    # Create JSONL format for different training scenarios
    jsonl_dir = data_dir / "jsonl"
    jsonl_dir.mkdir(exist_ok=True)
    
    formats = ["chat", "completion", "classification"]
    for format_type in formats:
        jsonl_path = jsonl_dir / f"training_data_{format_type}.jsonl"
        success = data_converter.convert_csv_to_jsonl(
            str(training_data_path),
            str(jsonl_path),
            format_type
        )
        if success:
            logger.info(f"Created {format_type} format: {jsonl_path}")
    
    # Step 5: Extract product categories
    logger.info("Step 5: Extracting product categories")
    categories = data_converter.extract_product_categories(str(training_data_path))
    
    # Save categories to a file
    categories_file = data_dir / "product_categories.txt"
    with open(categories_file, 'w') as f:
        for category in categories:
            f.write(f"{category}\n")
    
    logger.info(f"Saved {len(categories)} product categories to {categories_file}")
    
    # Step 6: Final validation
    logger.info("Step 6: Final validation")
    final_validation = data_converter.validate_training_data(str(training_data_path))
    
    if final_validation["valid"]:
        logger.info("✅ Conversion completed successfully!")
        logger.info(f"📊 Training data: {final_validation['total_records']} records")
        logger.info(f"🏷️  Product categories: {final_validation['unique_products']}")
        logger.info(f"📝 Average query length: {final_validation['avg_query_length']:.1f} characters")
        
        if final_validation.get('avg_answer_length', 0) > 0:
            logger.info(f"💬 Average answer length: {final_validation['avg_answer_length']:.1f} characters")
        
        logger.info("\n🚀 You can now start the MiniMind application!")
        logger.info("Run: docker-compose up --build")
        
        return True
    else:
        logger.error("❌ Final validation failed")
        logger.error(f"Issues: {final_validation.get('issues', [])}")
        return False

def check_requirements():
    """Check if all requirements are met"""
    logger.info("Checking requirements...")
    
    try:
        import torch
        import transformers
        import pandas
        logger.info("✅ All required packages are installed")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing required package: {e}")
        logger.error("Please install requirements: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    print("🔄 MiniMind Conversion Tool")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Run conversion
    try:
        success = main()
        if success:
            print("\n🎉 Conversion completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Conversion failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Conversion interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)
