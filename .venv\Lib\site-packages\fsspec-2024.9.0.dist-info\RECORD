fsspec-2024.9.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fsspec-2024.9.0.dist-info/METADATA,sha256=Jgjl4t19VdvyQ81LzLYeXagQpe2Om5QJDQ4nDfKdGbc,11749
fsspec-2024.9.0.dist-info/RECORD,,
fsspec-2024.9.0.dist-info/WHEEL,sha256=1yFddiXMmvYK7QYTqtRNtX66WJ0Mz8PYEiEUoOUUxRY,87
fsspec-2024.9.0.dist-info/licenses/LICENSE,sha256=LcNUls5TpzB5FcAIqESq1T53K0mzTN0ARFBnaRQH7JQ,1513
fsspec/__init__.py,sha256=l9MJaNNV2d4wKpCtMvXDr55n92DkdrAayGy3F9ICjzk,1998
fsspec/__pycache__/__init__.cpython-311.pyc,,
fsspec/__pycache__/_version.cpython-311.pyc,,
fsspec/__pycache__/archive.cpython-311.pyc,,
fsspec/__pycache__/asyn.cpython-311.pyc,,
fsspec/__pycache__/caching.cpython-311.pyc,,
fsspec/__pycache__/callbacks.cpython-311.pyc,,
fsspec/__pycache__/compression.cpython-311.pyc,,
fsspec/__pycache__/config.cpython-311.pyc,,
fsspec/__pycache__/conftest.cpython-311.pyc,,
fsspec/__pycache__/core.cpython-311.pyc,,
fsspec/__pycache__/dircache.cpython-311.pyc,,
fsspec/__pycache__/exceptions.cpython-311.pyc,,
fsspec/__pycache__/fuse.cpython-311.pyc,,
fsspec/__pycache__/generic.cpython-311.pyc,,
fsspec/__pycache__/gui.cpython-311.pyc,,
fsspec/__pycache__/json.cpython-311.pyc,,
fsspec/__pycache__/mapping.cpython-311.pyc,,
fsspec/__pycache__/parquet.cpython-311.pyc,,
fsspec/__pycache__/registry.cpython-311.pyc,,
fsspec/__pycache__/spec.cpython-311.pyc,,
fsspec/__pycache__/transaction.cpython-311.pyc,,
fsspec/__pycache__/utils.cpython-311.pyc,,
fsspec/_version.py,sha256=1O0P6wbwqtkyltAT0n4UISDOJbtOp9uOwvifb7ASk-U,417
fsspec/archive.py,sha256=S__DzfZj-urAN3tp2W6jJ6YDiXG1fAl7FjvWUN73qIE,2386
fsspec/asyn.py,sha256=MTe85f2Rmvwg-uhZbckpU_GemYYYSZ3AAj8Et9CCgmk,36390
fsspec/caching.py,sha256=x6IEdxtR3cMDjy40sNHyawR2SLtNSahVuP5i_TImdso,31600
fsspec/callbacks.py,sha256=BDIwLzK6rr_0V5ch557fSzsivCElpdqhXr5dZ9Te-EE,9210
fsspec/compression.py,sha256=jCSUMJu-zSNyrusnHT0wKXgOd1tTJR6vM126i5SR5Zc,4865
fsspec/config.py,sha256=LF4Zmu1vhJW7Je9Q-cwkRc3xP7Rhyy7Xnwj26Z6sv2g,4279
fsspec/conftest.py,sha256=fVfx-NLrH_OZS1TIpYNoPzM7efEcMoL62reHOdYeFCA,1245
fsspec/core.py,sha256=299qCp0H3w3e6zbiK5YOm3pJjxuPr4IYcK6Yg1Zgcos,23684
fsspec/dircache.py,sha256=YzogWJrhEastHU7vWz-cJiJ7sdtLXFXhEpInGKd4EcM,2717
fsspec/exceptions.py,sha256=pauSLDMxzTJMOjvX1WEUK0cMyFkrFxpWJsyFywav7A8,331
fsspec/fuse.py,sha256=Q-3NOOyLqBfYa4Db5E19z_ZY36zzYHtIs1mOUasItBQ,10177
fsspec/generic.py,sha256=AFbo-mHBt5QJV1Aplg5CJuUiiJ4bNQhcKRuwkZJdWac,13761
fsspec/gui.py,sha256=xBnHL2-r0LVwhDAtnHoPpXts7jd4Z32peawCJiI-7lI,13975
fsspec/implementations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fsspec/implementations/__pycache__/__init__.cpython-311.pyc,,
fsspec/implementations/__pycache__/arrow.cpython-311.pyc,,
fsspec/implementations/__pycache__/cache_mapper.cpython-311.pyc,,
fsspec/implementations/__pycache__/cache_metadata.cpython-311.pyc,,
fsspec/implementations/__pycache__/cached.cpython-311.pyc,,
fsspec/implementations/__pycache__/dask.cpython-311.pyc,,
fsspec/implementations/__pycache__/data.cpython-311.pyc,,
fsspec/implementations/__pycache__/dbfs.cpython-311.pyc,,
fsspec/implementations/__pycache__/dirfs.cpython-311.pyc,,
fsspec/implementations/__pycache__/ftp.cpython-311.pyc,,
fsspec/implementations/__pycache__/git.cpython-311.pyc,,
fsspec/implementations/__pycache__/github.cpython-311.pyc,,
fsspec/implementations/__pycache__/http.cpython-311.pyc,,
fsspec/implementations/__pycache__/jupyter.cpython-311.pyc,,
fsspec/implementations/__pycache__/libarchive.cpython-311.pyc,,
fsspec/implementations/__pycache__/local.cpython-311.pyc,,
fsspec/implementations/__pycache__/memory.cpython-311.pyc,,
fsspec/implementations/__pycache__/reference.cpython-311.pyc,,
fsspec/implementations/__pycache__/sftp.cpython-311.pyc,,
fsspec/implementations/__pycache__/smb.cpython-311.pyc,,
fsspec/implementations/__pycache__/tar.cpython-311.pyc,,
fsspec/implementations/__pycache__/webhdfs.cpython-311.pyc,,
fsspec/implementations/__pycache__/zip.cpython-311.pyc,,
fsspec/implementations/arrow.py,sha256=721Dikne_lV_0tlgk9jyKmHL6W-5MT0h2LKGvOYQTPI,8623
fsspec/implementations/cache_mapper.py,sha256=W4wlxyPxZbSp9ItJ0pYRVBMh6bw9eFypgP6kUYuuiI4,2421
fsspec/implementations/cache_metadata.py,sha256=pcOJYcBQY5OaC7Yhw0F3wjg08QLYApGmoISCrbs59ks,8511
fsspec/implementations/cached.py,sha256=t5atYATgjuABm-mUyReqjGqVyyP1XBSuROX92aMecxY,32826
fsspec/implementations/dask.py,sha256=CXZbJzIVOhKV8ILcxuy3bTvcacCueAbyQxmvAkbPkrk,4466
fsspec/implementations/data.py,sha256=LDLczxRh8h7x39Zjrd-GgzdQHr78yYxDlrv2C9Uxb5E,1658
fsspec/implementations/dbfs.py,sha256=a0eNjLxyfFK7pbEa52U8K-PhNHukzdGVx1eLcVniaXY,15092
fsspec/implementations/dirfs.py,sha256=VPSJhy2wFZodY5BB-o1tkWGYecvU2EmbgubmX3lOwuw,11815
fsspec/implementations/ftp.py,sha256=VpJWnQscdEKRu4fzkCtuf3jD9A74mBaerS2ijUwZ-_I,11936
fsspec/implementations/git.py,sha256=vKGI-Vd5q4H2RrvhebkPc9NwlfkZ980OUGhebeCw-M0,4034
fsspec/implementations/github.py,sha256=eAn1kJ7VeWR6gVoVRLBYclF_rQDXSJU-xzMXpvPQWqs,8002
fsspec/implementations/http.py,sha256=BjDJ72IoUCe_EHKc44J__sy8VHCOdc98gVEq27sWUk8,29695
fsspec/implementations/jupyter.py,sha256=B2uj7OEm7yIk-vRSsO37_ND0t0EBvn4B-Su43ibN4Pg,3811
fsspec/implementations/libarchive.py,sha256=5_I2DiLXwQ1JC8x-K7jXu-tBwhO9dj7tFLnb0bTnVMQ,7102
fsspec/implementations/local.py,sha256=DNBZhF9LYYTPR4PKedeWuk32Tztc9jlgXtGRFGX7nv4,15103
fsspec/implementations/memory.py,sha256=-0AedWR-jBaw2zamEuL4ku73lJQwRdp-Muia0u1j6pU,10170
fsspec/implementations/reference.py,sha256=43lG6cq9GP0JfMv_n_CyPznRcpEaWAaxG-rgeZt9BV4,44375
fsspec/implementations/sftp.py,sha256=fMY9XZcmpjszQ2tCqO_TPaJesaeD_Dv7ptYzgUPGoO0,5631
fsspec/implementations/smb.py,sha256=5fhu8h06nOLBPh2c48aT7WBRqh9cEcbIwtyu06wTjec,15236
fsspec/implementations/tar.py,sha256=dam78Tp_CozybNqCY2JYgGBS3Uc9FuJUAT9oB0lolOs,4111
fsspec/implementations/webhdfs.py,sha256=aet-AOfMoK91C3jNu5xBxK0Mu2iaAWiL9Xfu12KyjQI,16705
fsspec/implementations/zip.py,sha256=XoRukvrnJWngLbE8Exp2XCVf3SgSPmOqdeCqQ3NpSr0,6047
fsspec/json.py,sha256=65sQ0Y7mTj33u_Y4IId5up4abQ3bAel4E4QzbKMiQSg,3826
fsspec/mapping.py,sha256=CtD_GEmyYgXefQHndkxu7Zb_kbTS3mlFP2zIwlAoQTY,8289
fsspec/parquet.py,sha256=ONG29Enesp0ToCH2bQ7zkpimnVIsZ2S4xCLj35-fY78,19455
fsspec/registry.py,sha256=HVC-4HWDZnA6rycJwAu8F8ZXzON_85MTQVIyS6LOHxo,11320
fsspec/spec.py,sha256=A48RUDL50AwSOGB1VT114GC1TY93SGlr3fkBO1Yp0Fk,69572
fsspec/tests/abstract/__init__.py,sha256=o3rQBCeTTTdji0OxKdTvBvwL7q78sEIh5J5-Q-If6z0,10046
fsspec/tests/abstract/__pycache__/__init__.cpython-311.pyc,,
fsspec/tests/abstract/__pycache__/common.cpython-311.pyc,,
fsspec/tests/abstract/__pycache__/copy.cpython-311.pyc,,
fsspec/tests/abstract/__pycache__/get.cpython-311.pyc,,
fsspec/tests/abstract/__pycache__/mv.cpython-311.pyc,,
fsspec/tests/abstract/__pycache__/put.cpython-311.pyc,,
fsspec/tests/abstract/common.py,sha256=1GQwNo5AONzAnzZj0fWgn8NJPLXALehbsuGxS3FzWVU,4973
fsspec/tests/abstract/copy.py,sha256=gU5-d97U3RSde35Vp4RxPY4rWwL744HiSrJ8IBOp9-8,19967
fsspec/tests/abstract/get.py,sha256=vNR4HztvTR7Cj56AMo7_tx7TeYz1Jgr_2Wb8Lv-UiBY,20755
fsspec/tests/abstract/mv.py,sha256=k8eUEBIrRrGMsBY5OOaDXdGnQUKGwDIfQyduB6YD3Ns,1982
fsspec/tests/abstract/put.py,sha256=7aih17OKB_IZZh1Mkq1eBDIjobhtMQmI8x-Pw-S_aZk,21201
fsspec/transaction.py,sha256=xliRG6U2Zf3khG4xcw9WiB-yAoqJSHEGK_VjHOdtgo0,2398
fsspec/utils.py,sha256=dVaokocjhMOnO3B1KmKlgxYqojQJyzb3mgIfaAaz8Pk,22941
