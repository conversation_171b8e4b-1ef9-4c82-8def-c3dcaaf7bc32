# PowerShell script to convert model from .pth to .pt format

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Model Converter: .pth to .pt format" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Found Python: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "Error: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python from https://python.org" -ForegroundColor Yellow
    Write-Host "Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if convert_model.py exists
if (-not (Test-Path "convert_model.py")) {
    Write-Host "Error: convert_model.py not found in current directory" -ForegroundColor Red
    Write-Host "Expected location: $(Join-Path (Get-Location) 'convert_model.py')" -ForegroundColor Yellow
    Write-Host "Please make sure you're running this from the backend4c folder" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if model file exists
$modelPath = "app\data4c\results\model_distill_bert.pth"
if (Test-Path $modelPath) {
    $modelSize = [math]::Round((Get-Item $modelPath).Length / 1MB, 1)
    Write-Host "Found model file: $modelPath" -ForegroundColor Green
    Write-Host "Model size: $modelSize MB" -ForegroundColor Green
    Write-Host ""
} else {
    Write-Host "Warning: Default model file not found at $modelPath" -ForegroundColor Yellow
    Write-Host "You can still run the converter and specify a custom path" -ForegroundColor Yellow
    Write-Host ""
}

# Create directories if they don't exist
$dataDir = "app\data4c\results"
if (-not (Test-Path $dataDir)) {
    Write-Host "Creating directory: $dataDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
}

# Run the conversion
Write-Host "Running conversion..." -ForegroundColor Cyan
Write-Host "Command: python convert_model.py" -ForegroundColor Gray
Write-Host ""

try {
    python convert_model.py
    $exitCode = $LASTEXITCODE
    
    Write-Host ""
    if ($exitCode -eq 0) {
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "✅ Conversion completed successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        
        # Show converted file info
        $convertedPath = "app\data4c\results\model_distill_bert.pt"
        if (Test-Path $convertedPath) {
            $convertedSize = [math]::Round((Get-Item $convertedPath).Length / 1MB, 1)
            $convertedDate = (Get-Item $convertedPath).LastWriteTime
            Write-Host ""
            Write-Host "Converted file created:" -ForegroundColor Green
            Write-Host "  File: $convertedPath" -ForegroundColor White
            Write-Host "  Size: $convertedSize MB" -ForegroundColor White
            Write-Host "  Date: $convertedDate" -ForegroundColor White
        }
    } else {
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "❌ Conversion failed!" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "Common solutions:" -ForegroundColor Yellow
        Write-Host "1. Make sure the model file exists" -ForegroundColor White
        Write-Host "2. Install required packages: pip install torch transformers" -ForegroundColor White
        Write-Host "3. Check that you have enough memory" -ForegroundColor White
        Write-Host "4. Run from the correct directory (backend4c)" -ForegroundColor White
    }
} catch {
    Write-Host "Error running conversion: $_" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
