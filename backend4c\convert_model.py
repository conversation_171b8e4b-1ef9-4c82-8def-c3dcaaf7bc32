#!/usr/bin/env python3
"""
Simple script to convert model_distill_bert.pth to model_distill_bert.pt
"""

import os
import sys
import argparse
from app.utils.converter import convert_pth_to_pt

def main(args):
    # Get the model path
    if args.model_path:
        model_path = args.model_path
    else:
        model_directory = os.getenv('MODEL_DIRECTORY', './app/data4c/results')
        model_path = os.path.join(model_directory, 'model_distill_bert.pth')

    print(f"Looking for model at: {model_path}")

    # Check if the model exists
    if not os.path.exists(model_path):
        print(f"Error: Model file not found at {model_path}")
        print("Please make sure the model file exists before running the converter.")
        return False

    print("Model found! Starting conversion...")

    # Get file size for user info
    file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
    print(f"Model size: {file_size:.1f} MB")

    if args.num_labels:
        print(f"Using {args.num_labels} labels")
    else:
        print("Auto-detecting number of labels...")

    # Convert the model
    success = convert_pth_to_pt(model_path, num_labels=args.num_labels or 20)

    if success:
        output_path = os.path.splitext(model_path)[0] + '.pt'
        output_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"✅ Conversion successful!")
        print(f"📁 Original file: {model_path} ({file_size:.1f} MB)")
        print(f"📁 Converted file: {output_path} ({output_size:.1f} MB)")
        return True
    else:
        print("❌ Conversion failed!")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert .pth model to .pt format")
    parser.add_argument("--model-path", "-m", type=str, help="Path to the .pth model file")
    parser.add_argument("--num-labels", "-n", type=int, help="Number of labels in the model (auto-detect if not specified)")

    args = parser.parse_args()

    print("🔄 Model Converter: .pth to .pt")
    print("=" * 40)

    try:
        success = main(args)
        if success:
            print("\n🎉 Done!")
        else:
            print("\n💥 Failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Conversion interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)
