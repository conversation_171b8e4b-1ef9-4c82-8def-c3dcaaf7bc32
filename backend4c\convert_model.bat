@echo off
echo Converting model from .pth to .pt format...
echo.

REM Change to the directory where this batch file is located
cd /d "%~dp0"

REM Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Check if the convert_model.py file exists
if not exist "convert_model.py" (
    echo Error: convert_model.py not found in current directory
    echo Current directory: %CD%
    echo Please make sure you're running this from the correct folder
    pause
    exit /b 1
)

REM Try to run the conversion
echo Running conversion...
python convert_model.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Conversion completed successfully!
) else (
    echo.
    echo Conversion failed! Check the error messages above.
)

echo.
echo Press any key to exit...
pause >nul
