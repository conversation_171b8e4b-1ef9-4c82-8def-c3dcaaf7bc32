#!/usr/bin/env python3
"""
Simple test script to check if the converter can run
"""

import os
import sys

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch: {e}")
        return False
    
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except ImportError as e:
        print(f"❌ Transformers: {e}")
        return False
    
    try:
        from transformers import DistilBertForSequenceClassification
        print("✅ DistilBertForSequenceClassification imported")
    except ImportError as e:
        print(f"❌ DistilBertForSequenceClassification: {e}")
        return False
    
    return True

def test_paths():
    """Test if required paths exist"""
    print("\nTesting paths...")
    
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    # Check if we're in the right directory
    if not os.path.exists("convert_model.py"):
        print("❌ convert_model.py not found in current directory")
        return False
    else:
        print("✅ convert_model.py found")
    
    # Check app directory structure
    app_dir = "app"
    if not os.path.exists(app_dir):
        print(f"❌ {app_dir} directory not found")
        return False
    else:
        print(f"✅ {app_dir} directory found")
    
    # Check data directory
    data_dir = os.path.join("app", "data4c", "results")
    if not os.path.exists(data_dir):
        print(f"⚠️  {data_dir} directory not found - will be created")
        try:
            os.makedirs(data_dir, exist_ok=True)
            print(f"✅ Created {data_dir}")
        except Exception as e:
            print(f"❌ Failed to create {data_dir}: {e}")
            return False
    else:
        print(f"✅ {data_dir} directory found")
    
    # Check for model file
    model_path = os.path.join(data_dir, "model_distill_bert.pth")
    if os.path.exists(model_path):
        size_mb = os.path.getsize(model_path) / (1024 * 1024)
        print(f"✅ Model file found: {model_path} ({size_mb:.1f} MB)")
    else:
        print(f"⚠️  Model file not found: {model_path}")
        print("   This is OK if you plan to specify a custom path")
    
    return True

def test_converter_import():
    """Test if the converter module can be imported"""
    print("\nTesting converter import...")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.getcwd())
        
        from app.utils.converter import convert_pth_to_pt
        print("✅ Converter function imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import converter: {e}")
        
        # Try to diagnose the issue
        if not os.path.exists("app"):
            print("   Issue: 'app' directory not found")
        elif not os.path.exists("app/utils"):
            print("   Issue: 'app/utils' directory not found")
        elif not os.path.exists("app/utils/converter.py"):
            print("   Issue: 'app/utils/converter.py' file not found")
        elif not os.path.exists("app/utils/logger.py"):
            print("   Issue: 'app/utils/logger.py' file not found")
        else:
            print(f"   Unexpected import error: {e}")
        
        return False

def main():
    print("🔍 Converter Test Suite")
    print("=" * 40)
    
    all_tests_passed = True
    
    # Test 1: Imports
    if not test_imports():
        all_tests_passed = False
        print("\n❌ Import test failed")
        print("Solution: pip install torch transformers")
    
    # Test 2: Paths
    if not test_paths():
        all_tests_passed = False
        print("\n❌ Path test failed")
    
    # Test 3: Converter import
    if not test_converter_import():
        all_tests_passed = False
        print("\n❌ Converter import test failed")
    
    print("\n" + "=" * 40)
    if all_tests_passed:
        print("🎉 All tests passed! The converter should work.")
        print("\nYou can now run:")
        print("  python convert_model.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
    
    return all_tests_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)
