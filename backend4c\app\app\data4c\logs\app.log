2024-12-11 22:46:14,750 - app.database - INFO - Database backed up to D:\Gbc\4016-FullStack\FullStack4Chatbot\backend4c\app\data4c\db\backups\db4chatbot_20241211_224614.db
2024-12-11 22:46:14,751 - app.database - INFO - Database already exists at D:\Gbc\4016-FullStack\FullStack4Chatbot\backend4c\app\data4c\db\db4chatbot.db
2024-12-11 22:46:14,752 - app.main - INFO - Using existing database
2024-12-11 22:46:14,752 - app.classification.issue_classification - INFO - Using device: cpu
2024-12-11 22:46:14,753 - app.classification.issue_classification - INFO - Initialized with 20 product categories
2024-12-11 22:46:15,066 - app.classification.issue_classification - INFO - No saved model found. Model will need training before use.
2024-12-11 22:46:15,069 - app.main - INFO - Startup completed successfully
2024-12-11 22:46:29,592 - app.routers.user_router - INFO - Successful login for user: <EMAIL> (ID: 1)
2024-12-11 22:46:47,076 - app.routers.issue_router - INFO - Received classification request: Is the Mouse compatible with wireless printing?
2024-12-11 22:46:47,077 - app.classification.issue_classification - INFO - Classifying query: Is the Mouse compatible with wireless printing?
2024-12-11 22:46:47,261 - app.classification.issue_classification - INFO - Classification details for: 'Is the Mouse compatible with wireless printing?'
2024-12-11 22:46:47,262 - app.classification.issue_classification - INFO - Predicted class: Fax machine (code: 7)
2024-12-11 22:46:47,262 - app.classification.issue_classification - INFO - Top 3 predictions:
2024-12-11 22:46:47,263 - app.classification.issue_classification - INFO - Fax machine: 0.061
2024-12-11 22:46:47,264 - app.classification.issue_classification - INFO - Mouse: 0.059
2024-12-11 22:46:47,264 - app.classification.issue_classification - INFO - Keyboard: 0.057
2024-12-11 22:46:47,265 - app.routers.issue_router - INFO - Classification result: Fax machine (code: 7)
2024-12-11 22:47:13,610 - app.routers.issue_router - INFO - Received classification request: How to calibrate the color settings on the Fax machine?
2024-12-11 22:47:13,611 - app.classification.issue_classification - INFO - Classifying query: How to calibrate the color settings on the Fax machine?
2024-12-11 22:47:13,795 - app.classification.issue_classification - INFO - Classification details for: 'How to calibrate the color settings on the Fax machine?'
2024-12-11 22:47:13,796 - app.classification.issue_classification - INFO - Predicted class: Mouse (code: 5)
2024-12-11 22:47:13,797 - app.classification.issue_classification - INFO - Top 3 predictions:
2024-12-11 22:47:13,797 - app.classification.issue_classification - INFO - Mouse: 0.061
2024-12-11 22:47:13,798 - app.classification.issue_classification - INFO - Fax machine: 0.060
2024-12-11 22:47:13,798 - app.classification.issue_classification - INFO - Monitor: 0.056
2024-12-11 22:47:13,800 - app.routers.issue_router - INFO - Classification result: Mouse (code: 5)
2024-12-11 22:47:38,183 - app.routers.issue_router - INFO - Received classification request: How to connect the Printer to a computer?
2024-12-11 22:47:38,183 - app.classification.issue_classification - INFO - Classifying query: How to connect the Printer to a computer?
2024-12-11 22:47:38,321 - app.classification.issue_classification - INFO - Classification details for: 'How to connect the Printer to a computer?'
2024-12-11 22:47:38,322 - app.classification.issue_classification - INFO - Predicted class: Fax machine (code: 7)
2024-12-11 22:47:38,323 - app.classification.issue_classification - INFO - Top 3 predictions:
2024-12-11 22:47:38,323 - app.classification.issue_classification - INFO - Fax machine: 0.061
2024-12-11 22:47:38,324 - app.classification.issue_classification - INFO - Mouse: 0.061
2024-12-11 22:47:38,324 - app.classification.issue_classification - INFO - Conference phone: 0.057
2024-12-11 22:47:38,324 - app.routers.issue_router - INFO - Classification result: Fax machine (code: 7)
2025-07-10 10:55:34,274 - app.utils.converter - INFO - Converting model from ./app/data4c/results\model_distill_bert.pth to TorchScript format
2025-07-10 10:55:34,274 - app.utils.converter - INFO - Loading model...
2025-07-10 10:55:35,246 - app.utils.converter - INFO - Converting to TorchScript...
2025-07-10 10:55:35,432 - app.utils.converter - ERROR - Error converting model: Comprehension ifs are not supported yet:
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\transformers\models\distilbert\modeling_distilbert.py", line 575
    
        if not return_dict:
            return tuple(v for v in [hidden_state, all_hidden_states, all_attentions] if v is not None)
        return BaseModelOutput(
            last_hidden_state=hidden_state, hidden_states=all_hidden_states, attentions=all_attentions
Traceback (most recent call last):
  File "D:\Gbc\4016-FullStack\FullStack4Chatbot\backend4c\app\utils\converter.py", line 59, in convert_pth_to_pt
    scripted_model = torch.jit.script(model)
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_script.py", line 1432, in script
    return _script_impl(
           ^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_script.py", line 1146, in _script_impl
    return torch.jit._recursive.create_script_module(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 559, in create_script_module
    return create_script_module_impl(nn_module, concrete_type, stubs_fn)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 632, in create_script_module_impl
    script_module = torch.jit.RecursiveScriptModule._construct(cpp_module, init_fn)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_script.py", line 649, in _construct
    init_fn(script_module)
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 608, in init_fn
    scripted = create_script_module_impl(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 632, in create_script_module_impl
    script_module = torch.jit.RecursiveScriptModule._construct(cpp_module, init_fn)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_script.py", line 649, in _construct
    init_fn(script_module)
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 608, in init_fn
    scripted = create_script_module_impl(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 572, in create_script_module_impl
    method_stubs = stubs_fn(nn_module)
                   ^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 895, in infer_methods_to_compile
    stubs.append(make_stub_from_method(nn_module, method))
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 88, in make_stub_from_method
    return make_stub(func, method_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\_recursive.py", line 72, in make_stub
    ast = get_jit_def(func, name, self_name="RecursiveScriptModule")
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 373, in get_jit_def
    return build_def(
           ^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 434, in build_def
    return Def(Ident(r, def_name), decl, build_stmts(ctx, body))
                                         ^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 196, in build_stmts
    stmts = [build_stmt(ctx, s) for s in stmts]
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 196, in <listcomp>
    stmts = [build_stmt(ctx, s) for s in stmts]
             ^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 407, in __call__
    return method(ctx, node)
           ^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 783, in build_If
    build_stmts(ctx, stmt.body),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 196, in build_stmts
    stmts = [build_stmt(ctx, s) for s in stmts]
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 196, in <listcomp>
    stmts = [build_stmt(ctx, s) for s in stmts]
             ^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 407, in __call__
    return method(ctx, node)
           ^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 722, in build_Return
    return Return(r, None if stmt.value is None else build_expr(ctx, stmt.value))
                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 407, in __call__
    return method(ctx, node)
           ^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 891, in build_Call
    args = [build_expr(ctx, py_arg) for py_arg in expr.args]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 891, in <listcomp>
    args = [build_expr(ctx, py_arg) for py_arg in expr.args]
            ^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 407, in __call__
    return method(ctx, node)
           ^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 1236, in build_GeneratorExp
    return ExprBuilder.build_ListComp(ctx, stmt)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\torch\jit\frontend.py", line 1225, in build_ListComp
    raise NotSupportedError(r, "Comprehension ifs are not supported yet")
torch.jit.frontend.NotSupportedError: Comprehension ifs are not supported yet:
  File "d:\Gbc\4016-FullStack\FullStack4Chatbot\.venv\Lib\site-packages\transformers\models\distilbert\modeling_distilbert.py", line 575
    
        if not return_dict:
            return tuple(v for v in [hidden_state, all_hidden_states, all_attentions] if v is not None)
        return BaseModelOutput(
            last_hidden_state=hidden_state, hidden_states=all_hidden_states, attentions=all_attentions

2025-07-10 10:59:27,918 - app.utils.converter - INFO - Converting model from ./app/data4c/results\model_distill_bert.pth to TorchScript format
2025-07-10 10:59:27,919 - app.utils.converter - INFO - Loading model...
2025-07-10 10:59:28,358 - app.utils.converter - INFO - Converting to TorchScript using tracing...
2025-07-10 10:59:29,366 - app.utils.converter - INFO - Saving TorchScript model to ./app/data4c/results\model_distill_bert.pt
2025-07-10 10:59:29,809 - app.utils.converter - INFO - Verifying saved model...
2025-07-10 10:59:30,275 - app.utils.converter - ERROR - Error converting model: 'dict' object has no attribute 'logits'
Traceback (most recent call last):
  File "D:\Gbc\4016-FullStack\FullStack4Chatbot\backend4c\app\utils\converter.py", line 88, in convert_pth_to_pt
    logger.info(f"Model verification successful - output shape: {test_output.logits.shape}")
                                                                 ^^^^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'logits'
2025-07-10 11:01:02,498 - app.utils.converter - INFO - Converting model from ./app/data4c/results\model_distill_bert.pth to TorchScript format
2025-07-10 11:01:02,498 - app.utils.converter - INFO - Output file ./app/data4c/results\model_distill_bert.pt already exists. Overwriting...
2025-07-10 11:01:02,498 - app.utils.converter - INFO - Loading model...
2025-07-10 11:01:02,976 - app.utils.converter - INFO - Converting to TorchScript using tracing...
2025-07-10 11:01:03,803 - app.utils.converter - INFO - Saving TorchScript model to ./app/data4c/results\model_distill_bert.pt
2025-07-10 11:01:04,240 - app.utils.converter - INFO - Verifying saved model...
2025-07-10 11:01:04,557 - app.utils.converter - INFO - Model verification successful - output shape: torch.Size([1, 20])
2025-07-10 11:01:04,569 - app.utils.converter - INFO - Conversion completed successfully
