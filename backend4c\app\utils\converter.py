import os
import torch
from transformers import DistilBertForSequenceClassification
from .logger import setup_logger

logger = setup_logger(__name__)

def convert_pth_to_pt(model_path, num_labels=20):
    """
    Convert a .pth model file to .pt format (TorchScript)

    Args:
        model_path: Path to the .pth model file
        num_labels: Number of labels for the classification model

    Returns:
        bool: True if conversion successful, False otherwise
    """
    try:
        logger.info(f"Converting model from {model_path} to TorchScript format")

        # Get the output path (same directory, just change extension)
        output_path = os.path.splitext(model_path)[0] + '.pt'

        # Check if output already exists
        if os.path.exists(output_path):
            logger.info(f"Output file {output_path} already exists. Overwriting...")

        # Load the model
        logger.info("Loading model...")
        try:
            # Try to load the model with the specified number of labels
            model = DistilBertForSequenceClassification.from_pretrained(
                'distilbert-base-uncased',
                num_labels=num_labels
            )
            # Use weights_only=True for security
            model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu'), weights_only=True))
        except Exception as e:
            logger.warning(f"Failed to load with {num_labels} labels, trying to auto-detect...")
            # Try to load the state dict first to get the number of labels
            state_dict = torch.load(model_path, map_location=torch.device('cpu'), weights_only=True)

            # Get the number of labels from the classifier layer
            if 'classifier.weight' in state_dict:
                detected_num_labels = state_dict['classifier.weight'].shape[0]
                logger.info(f"Auto-detected {detected_num_labels} labels from model")
                model = DistilBertForSequenceClassification.from_pretrained(
                    'distilbert-base-uncased',
                    num_labels=detected_num_labels
                )
                model.load_state_dict(state_dict)
            else:
                raise e

        model.eval()

        # Convert to TorchScript using tracing instead of scripting
        # This avoids the comprehension issue with torch.jit.script
        logger.info("Converting to TorchScript using tracing...")

        # Create a sample input for tracing
        # DistilBERT expects input_ids and attention_mask
        batch_size = 1
        seq_length = 128
        sample_input_ids = torch.randint(0, 1000, (batch_size, seq_length))
        sample_attention_mask = torch.ones(batch_size, seq_length)

        # Trace the model
        with torch.no_grad():
            traced_model = torch.jit.trace(
                model,
                (sample_input_ids, sample_attention_mask),
                strict=False  # Allow some flexibility in tracing
            )

        # Save the model
        logger.info(f"Saving TorchScript model to {output_path}")
        traced_model.save(output_path)

        # Verify the saved model
        logger.info("Verifying saved model...")
        test_model = torch.jit.load(output_path)

        # Test the loaded model with sample input
        with torch.no_grad():
            test_output = test_model(sample_input_ids, sample_attention_mask)
            # Handle both dict and object outputs
            if isinstance(test_output, dict):
                if 'logits' in test_output:
                    logger.info(f"Model verification successful - output shape: {test_output['logits'].shape}")
                else:
                    logger.info(f"Model verification successful - output keys: {list(test_output.keys())}")
            else:
                if hasattr(test_output, 'logits'):
                    logger.info(f"Model verification successful - output shape: {test_output.logits.shape}")
                else:
                    logger.info(f"Model verification successful - output type: {type(test_output)}")

        logger.info("Conversion completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error converting model: {str(e)}", exc_info=True)
        return False