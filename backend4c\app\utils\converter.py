import os
import torch
from transformers import DistilBertForSequenceClassification
from .logger import setup_logger

logger = setup_logger(__name__)

def convert_pth_to_pt(model_path, num_labels=20):
    """
    Convert a .pth model file to .pt format (TorchScript)

    Args:
        model_path: Path to the .pth model file
        num_labels: Number of labels for the classification model

    Returns:
        bool: True if conversion successful, False otherwise
    """
    try:
        logger.info(f"Converting model from {model_path} to TorchScript format")

        # Get the output path (same directory, just change extension)
        output_path = os.path.splitext(model_path)[0] + '.pt'

        # Check if output already exists
        if os.path.exists(output_path):
            logger.info(f"Output file {output_path} already exists. Overwriting...")

        # Load the model
        logger.info("Loading model...")
        try:
            # Try to load the model with the specified number of labels
            model = DistilBertForSequenceClassification.from_pretrained(
                'distilbert-base-uncased',
                num_labels=num_labels
            )
            model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
        except Exception as e:
            logger.warning(f"Failed to load with {num_labels} labels, trying to auto-detect...")
            # Try to load the state dict first to get the number of labels
            state_dict = torch.load(model_path, map_location=torch.device('cpu'))

            # Get the number of labels from the classifier layer
            if 'classifier.weight' in state_dict:
                detected_num_labels = state_dict['classifier.weight'].shape[0]
                logger.info(f"Auto-detected {detected_num_labels} labels from model")
                model = DistilBertForSequenceClassification.from_pretrained(
                    'distilbert-base-uncased',
                    num_labels=detected_num_labels
                )
                model.load_state_dict(state_dict)
            else:
                raise e

        model.eval()

        # Convert to TorchScript
        logger.info("Converting to TorchScript...")
        scripted_model = torch.jit.script(model)

        # Save the model
        logger.info(f"Saving TorchScript model to {output_path}")
        scripted_model.save(output_path)

        # Verify the saved model
        logger.info("Verifying saved model...")
        test_model = torch.jit.load(output_path)
        logger.info("Model verification successful")

        logger.info("Conversion completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error converting model: {str(e)}", exc_info=True)
        return False