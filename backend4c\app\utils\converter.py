import os
import torch
import pandas as pd
import json
import csv
from typing import List, Dict, Any, Optional, Tu<PERSON>
from transformers import <PERSON>Token<PERSON>, AutoModelForCausalLM
from .logger import setup_logger

logger = setup_logger(__name__)

class DataConverter:
    """
    Utility class for converting data between different formats and models
    """

    def __init__(self):
        self.logger = logger

    def convert_distilbert_to_minimind_data(self,
                                          input_csv_path: str,
                                          output_csv_path: str) -> bool:
        """
        Convert DistilBERT training data format to MiniMind format

        Args:
            input_csv_path: Path to the original DistilBERT training data
            output_csv_path: Path to save the converted MiniMind training data

        Returns:
            bool: True if conversion successful, False otherwise
        """
        try:
            self.logger.info(f"Converting DistilBERT data from {input_csv_path} to MiniMind format")

            # Read the original data
            df = pd.read_csv(input_csv_path)

            # Check if required columns exist
            required_columns = ['Query', 'Product']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                self.logger.error(f"Missing required columns: {missing_columns}")
                return False

            # Add Answer column if it doesn't exist
            if 'Answer' not in df.columns:
                df['Answer'] = df.apply(
                    lambda row: f"This appears to be a {row['Product']} related issue. I'll help you resolve it.",
                    axis=1
                )
                self.logger.info("Added default Answer column")

            # Save the converted data
            df.to_csv(output_csv_path, index=False)
            self.logger.info(f"Successfully converted {len(df)} records to {output_csv_path}")

            return True

        except Exception as e:
            self.logger.error(f"Error converting data: {str(e)}", exc_info=True)
            return False

    def convert_csv_to_jsonl(self,
                           csv_path: str,
                           jsonl_path: str,
                           format_type: str = "chat") -> bool:
        """
        Convert CSV training data to JSONL format for different training frameworks

        Args:
            csv_path: Path to the CSV file
            jsonl_path: Path to save the JSONL file
            format_type: Type of format ("chat", "completion", "classification")

        Returns:
            bool: True if conversion successful, False otherwise
        """
        try:
            self.logger.info(f"Converting CSV to JSONL format: {format_type}")

            df = pd.read_csv(csv_path)

            with open(jsonl_path, 'w', encoding='utf-8') as f:
                for _, row in df.iterrows():
                    if format_type == "chat":
                        # Chat format for conversational training
                        data = {
                            "messages": [
                                {"role": "user", "content": row['Query']},
                                {"role": "assistant", "content": row.get('Answer', f"This is a {row['Product']} related issue.")}
                            ]
                        }
                    elif format_type == "completion":
                        # Completion format for text generation
                        data = {
                            "prompt": f"Customer query: {row['Query']}\nProduct category: {row['Product']}\nResponse:",
                            "completion": row.get('Answer', f"This is a {row['Product']} related issue.")
                        }
                    elif format_type == "classification":
                        # Classification format
                        data = {
                            "text": row['Query'],
                            "label": row['Product']
                        }
                    else:
                        self.logger.error(f"Unknown format type: {format_type}")
                        return False

                    f.write(json.dumps(data, ensure_ascii=False) + '\n')

            self.logger.info(f"Successfully converted to JSONL: {jsonl_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error converting CSV to JSONL: {str(e)}", exc_info=True)
            return False

    def migrate_distilbert_to_minimind(self,
                                     old_model_path: str,
                                     new_model_path: str,
                                     training_data_path: str) -> bool:
        """
        Migrate from DistilBERT model to MiniMind model

        Args:
            old_model_path: Path to the old DistilBERT model
            new_model_path: Path to save the new MiniMind model
            training_data_path: Path to training data for fine-tuning

        Returns:
            bool: True if migration successful, False otherwise
        """
        try:
            self.logger.info("Starting migration from DistilBERT to MiniMind")

            # Check if old model exists
            if os.path.exists(old_model_path):
                self.logger.info(f"Found existing DistilBERT model at {old_model_path}")
                # Create backup
                backup_path = f"{old_model_path}.backup"
                if not os.path.exists(backup_path):
                    import shutil
                    shutil.copy2(old_model_path, backup_path)
                    self.logger.info(f"Created backup at {backup_path}")

            # Initialize MiniMind model
            self.logger.info("Downloading and initializing MiniMind model")
            model = AutoModelForCausalLM.from_pretrained("jingyaogong/MiniMind-V1")
            tokenizer = AutoTokenizer.from_pretrained("jingyaogong/MiniMind-V1")

            # Save the model locally
            os.makedirs(os.path.dirname(new_model_path), exist_ok=True)
            model.save_pretrained(new_model_path)
            tokenizer.save_pretrained(new_model_path)

            self.logger.info(f"MiniMind model saved to {new_model_path}")

            # If training data exists, we could fine-tune here
            if os.path.exists(training_data_path):
                self.logger.info("Training data found. Model can be fine-tuned later.")

            return True

        except Exception as e:
            self.logger.error(f"Error during migration: {str(e)}", exc_info=True)
            return False

    def extract_product_categories(self, csv_path: str) -> List[str]:
        """
        Extract unique product categories from training data

        Args:
            csv_path: Path to the CSV file

        Returns:
            List[str]: List of unique product categories
        """
        try:
            df = pd.read_csv(csv_path)
            if 'Product' in df.columns:
                categories = df['Product'].unique().tolist()
                self.logger.info(f"Found {len(categories)} product categories")
                return sorted(categories)
            else:
                self.logger.error("Product column not found in CSV")
                return []
        except Exception as e:
            self.logger.error(f"Error extracting categories: {str(e)}")
            return []

    def validate_training_data(self, csv_path: str) -> Dict[str, Any]:
        """
        Validate training data format and quality

        Args:
            csv_path: Path to the CSV file

        Returns:
            Dict[str, Any]: Validation results
        """
        try:
            df = pd.read_csv(csv_path)

            results = {
                "valid": True,
                "total_records": len(df),
                "missing_queries": 0,
                "missing_products": 0,
                "missing_answers": 0,
                "unique_products": 0,
                "avg_query_length": 0,
                "avg_answer_length": 0,
                "issues": []
            }

            # Check required columns
            required_columns = ['Query', 'Product']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                results["valid"] = False
                results["issues"].append(f"Missing required columns: {missing_columns}")
                return results

            # Check for missing values
            results["missing_queries"] = df['Query'].isna().sum()
            results["missing_products"] = df['Product'].isna().sum()

            if 'Answer' in df.columns:
                results["missing_answers"] = df['Answer'].isna().sum()

            # Calculate statistics
            results["unique_products"] = df['Product'].nunique()
            results["avg_query_length"] = df['Query'].str.len().mean()

            if 'Answer' in df.columns:
                results["avg_answer_length"] = df['Answer'].str.len().mean()

            # Check for issues
            if results["missing_queries"] > 0:
                results["issues"].append(f"{results['missing_queries']} records with missing queries")

            if results["missing_products"] > 0:
                results["issues"].append(f"{results['missing_products']} records with missing products")

            if results["avg_query_length"] < 10:
                results["issues"].append("Average query length is very short")

            if results["unique_products"] < 2:
                results["issues"].append("Very few product categories found")

            self.logger.info(f"Validation completed: {results}")
            return results

        except Exception as e:
            self.logger.error(f"Error validating data: {str(e)}")
            return {"valid": False, "error": str(e)}

    def create_sample_data(self, output_path: str, num_samples: int = 50) -> bool:
        """
        Create sample training data for testing

        Args:
            output_path: Path to save the sample data
            num_samples: Number of sample records to create

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Creating {num_samples} sample records")

            # Sample data templates
            templates = [
                ("How do I fix my {product}?", "{product}", "To fix your {product}, try restarting it first, then check all connections and update drivers if needed."),
                ("My {product} is not working", "{product}", "If your {product} is not working, please check the power connection and try restarting the device."),
                ("How to connect {product} to WiFi?", "{product}", "To connect your {product} to WiFi, access the network settings and follow the setup wizard."),
                ("{product} making strange noise", "{product}", "If your {product} is making strange noises, it may need cleaning or professional service."),
                ("How to update {product} drivers?", "{product}", "To update {product} drivers, visit the manufacturer's website or use the device manager."),
            ]

            products = [
                "Printer", "Scanner", "Laptop", "Monitor", "Keyboard",
                "Mouse", "Projector", "Fax", "Calculator", "Shredder"
            ]

            data = []
            for i in range(num_samples):
                template = templates[i % len(templates)]
                product = products[i % len(products)]

                query = template[0].format(product=product)
                product_name = template[1].format(product=product)
                answer = template[2].format(product=product)

                data.append({
                    "Query": query,
                    "Product": product_name,
                    "Answer": answer
                })

            df = pd.DataFrame(data)
            df.to_csv(output_path, index=False)

            self.logger.info(f"Sample data created at {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error creating sample data: {str(e)}")
            return False

class ModelConverter:
    """
    Utility class for converting between different model formats
    """

    def __init__(self):
        self.logger = logger

    def convert_pytorch_to_onnx(self,
                              model_path: str,
                              output_path: str,
                              sample_input: Optional[torch.Tensor] = None) -> bool:
        """
        Convert PyTorch model to ONNX format

        Args:
            model_path: Path to the PyTorch model
            output_path: Path to save the ONNX model
            sample_input: Sample input tensor for tracing

        Returns:
            bool: True if conversion successful, False otherwise
        """
        try:
            self.logger.info(f"Converting PyTorch model to ONNX: {model_path}")

            # Load the model
            model = torch.load(model_path, map_location='cpu')
            model.eval()

            # Create sample input if not provided
            if sample_input is None:
                sample_input = torch.randint(0, 1000, (1, 128))  # Sample tokenized input

            # Export to ONNX
            torch.onnx.export(
                model,
                sample_input,
                output_path,
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=['input'],
                output_names=['output'],
                dynamic_axes={
                    'input': {0: 'batch_size', 1: 'sequence'},
                    'output': {0: 'batch_size'}
                }
            )

            self.logger.info(f"ONNX model saved to {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error converting to ONNX: {str(e)}")
            return False

    def convert_to_torchscript(self,
                             model_path: str,
                             output_path: str) -> bool:
        """
        Convert model to TorchScript format for deployment

        Args:
            model_path: Path to the original model
            output_path: Path to save the TorchScript model

        Returns:
            bool: True if conversion successful, False otherwise
        """
        try:
            self.logger.info(f"Converting model to TorchScript: {model_path}")

            # Load the model
            model = torch.load(model_path, map_location='cpu')
            model.eval()

            # Convert to TorchScript
            scripted_model = torch.jit.script(model)
            scripted_model.save(output_path)

            self.logger.info(f"TorchScript model saved to {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error converting to TorchScript: {str(e)}")
            return False

# Convenience functions
def convert_distilbert_data(input_path: str, output_path: str) -> bool:
    """Convert DistilBERT training data to MiniMind format"""
    converter = DataConverter()
    return converter.convert_distilbert_to_minimind_data(input_path, output_path)

def migrate_to_minimind(old_model_path: str, new_model_path: str, training_data_path: str) -> bool:
    """Migrate from DistilBERT to MiniMind"""
    converter = DataConverter()
    return converter.migrate_distilbert_to_minimind(old_model_path, new_model_path, training_data_path)

def validate_data(csv_path: str) -> Dict[str, Any]:
    """Validate training data"""
    converter = DataConverter()
    return converter.validate_training_data(csv_path)

def create_sample_data(output_path: str, num_samples: int = 50) -> bool:
    """Create sample training data"""
    converter = DataConverter()
    return converter.create_sample_data(output_path, num_samples)